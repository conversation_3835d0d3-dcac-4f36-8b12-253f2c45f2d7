import dayjs from 'dayjs'
import { isEmpty, trim } from 'lodash'

export const formatDate = (dateString: any = '', twoDigitYear = true) => {
    if (isEmpty(trim(dateString))) {
        return ''
    }

    // For date-only strings (YYYY-MM-DD), format directly with dayjs to avoid timezone issues
    if (
        typeof dateString === 'string' &&
        /^\d{4}-\d{2}-\d{2}$/.test(dateString)
    ) {
        const dayjsDate = dayjs(dateString)
        const day = dayjsDate.format('DD')
        const month = dayjsDate.format('MM')
        const year = twoDigitYear
            ? dayjsDate.format('YY')
            : dayjsDate.format('YYYY')

        // Return in DD/MM/YY or DD/MM/YYYY format (Australian/NZ preference from memories)
        return `${day}/${month}/${year}`
    }

    // For other date formats, use dayjs to parse and format
    let dayjsDate: any

    if (dateString && typeof dateString === 'object') {
        // Handle object input
        dayjsDate = dayjs(dateString.toString())
    } else {
        // Handle other string formats or date objects
        dayjsDate = dayjs(dateString)
    }

    if (!dayjsDate.isValid()) {
        return ''
    }

    const day = dayjsDate.format('DD')
    const month = dayjsDate.format('MM')
    const year = twoDigitYear
        ? dayjsDate.format('YY')
        : dayjsDate.format('YYYY')

    // Return in DD/MM/YY or DD/MM/YYYY format
    return `${day}/${month}/${year}`
}

export const formatDateTime = (dateString: any = '', twoDigitYear = true) => {
    if (isEmpty(trim(dateString))) {
        return ''
    }
    const date =
        typeof dateString === 'object'
            ? new Date(dayjs(dateString).format('YYYY-MM-DD HH:mm:ss'))
            : new Date(dateString)
    const options = {
        year: twoDigitYear ? '2-digit' : 'numeric',
        month: 'numeric',
        day: 'numeric',
        hour: 'numeric',
        minute: 'numeric',
        hour12: false,
    } as const
    const formattedDate = date.toLocaleDateString(undefined, options)
    return formattedDate
}

export const formatDBDateTime = (dateString: any = '') => {
    if (isEmpty(trim(dateString))) {
        return ''
    }
    return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss')
}
