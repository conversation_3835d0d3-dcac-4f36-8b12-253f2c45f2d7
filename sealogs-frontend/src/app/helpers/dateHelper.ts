import dayjs from 'dayjs'
import { isEmpty, trim } from 'lodash'

export const formatDate = (dateString: any = '', twoDigitYear = true) => {
    if (isEmpty(trim(dateString))) {
        return ''
    }

    let date: Date

    if (dateString && typeof dateString === 'object') {
        // Handle object input
        date = new Date(dayjs(dateString.toString()).format('YYYY-MM-DD'))
    } else if (
        typeof dateString === 'string' &&
        /^\d{4}-\d{2}-\d{2}$/.test(dateString)
    ) {
        // Handle date-only strings (YYYY-MM-DD) as local dates to avoid timezone conversion issues
        const [year, month, day] = dateString.split('-').map(Number)
        date = new Date(year, month - 1, day) // month is 0-indexed in Date constructor
        console.log(
            'Created local date for date-only string:',
            date,
            'from:',
            dateString,
        )
    } else {
        // Handle other string formats or date objects
        date = new Date(dateString)
    }

    const timezone = localStorage.getItem('timezone') || 'Pacific/Auckland' // default to Pacific/Auckland if not set
    console.log('Raw localStorage timezone:', localStorage.getItem('timezone'))
    console.log('Final timezone value:', timezone)

    const options = {
        year: twoDigitYear ? '2-digit' : 'numeric',
        month: 'numeric',
        day: 'numeric',
        timeZone: timezone,
    } as const

    console.log('Options object after creation:', JSON.stringify(options))
    console.log('Date before formatting:', date)
    console.log('Options before formatting:', JSON.stringify(options))

    const formattedDate = new Intl.DateTimeFormat(undefined, options).format(
        date,
    )

    console.log('Date after formatting:', date)
    console.log('Options after formatting:', JSON.stringify(options))
    console.log({
        dateString,
        date,
        timezone,
        options,
        formattedDate,
    })
    return formattedDate
}

export const formatDateTime = (dateString: any = '', twoDigitYear = true) => {
    if (isEmpty(trim(dateString))) {
        return ''
    }
    const date =
        typeof dateString === 'object'
            ? new Date(dayjs(dateString).format('YYYY-MM-DD HH:mm:ss'))
            : new Date(dateString)
    const options = {
        year: twoDigitYear ? '2-digit' : 'numeric',
        month: 'numeric',
        day: 'numeric',
        hour: 'numeric',
        minute: 'numeric',
        hour12: false,
    } as const
    const formattedDate = date.toLocaleDateString(undefined, options)
    return formattedDate
}

export const formatDBDateTime = (dateString: any = '') => {
    if (isEmpty(trim(dateString))) {
        return ''
    }
    return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss')
}
